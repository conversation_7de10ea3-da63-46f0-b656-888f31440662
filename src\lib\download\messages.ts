import type { PageMessage, DownloadItem, CustomEventDetail } from '@/types/download';

// 发送消息到内容脚本的函数
export function sendToContentScript(message: PageMessage): void {
  // 使用当前窗口的origin，避免消息发送到其他标签页
  window.postMessage(message, window.location.origin);
}

// 创建消息监听器
export function createMessageListener(
  onMessage: (event: MessageEvent) => void
): () => void {
  const listener = (event: MessageEvent) => {
    if (event.source !== window) return;
    onMessage(event);
  };

  window.addEventListener('message', listener);

  // 返回清理函数
  return () => {
    window.removeEventListener('message', listener);
  };
}

// 触发自定义事件
export function dispatchCustomEvent(eventType: string, detail: CustomEventDetail): void {
  window.dispatchEvent(new CustomEvent(eventType, { detail }));
}

// 从URL参数中获取请求ID
export function getRequestIdFromUrl(): string | null {
  try {
    const urlParams = new URLSearchParams(window.location.search);
    const requestId = urlParams.get('requestId');
    return requestId;
  } catch (error) {
    console.error('解析URL参数失败:', error);
    return null;
  }
}

// 生成页面任务ID
export function generatePageTaskId(): string {
  return `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 请求下载数据
export function requestDownloadData(
  requestId: string,
  _pageTaskId: string,
  onSuccess: (data: DownloadItem) => void,
  onError: (error: string) => void,
  timeout: number = 2000 // 默认2秒超时
): () => void {
  let isCompleted = false;

  // 设置超时处理
  const timeoutId = setTimeout(() => {
    if (!isCompleted) {
      isCompleted = true;
      console.error('请求下载数据超时');
      onError('请求下载数据超时，请检查扩展是否正确安装');
      cleanup();
    }
  }, timeout);

  // 监听来自内容脚本的响应
  const cleanup = createMessageListener((event) => {
    const { type, ...messageData } = event.data;

    // 收到任何消息都清除超时
    clearTimeout(timeoutId);

    if (type === 'DOWNLOAD_DATA_RESPONSE' && !isCompleted) {
      isCompleted = true;

      // 从插件端获取的响应数据
      const responseData = messageData.data || messageData;

      if (responseData.success) {
        // 确保下载数据包含requestId
        const downloadData = responseData.data;
        if (!downloadData.requestId) {
          downloadData.requestId = requestId; // 使用从URL获取的requestId
        }

        onSuccess(downloadData);
      } else {
        console.error('获取下载数据失败:', responseData.error);
        onError(responseData.error || '获取下载数据失败');
      }
      cleanup();
    }
  });


  // 向内容脚本发送请求
  sendToContentScript({
    type: 'GET_DOWNLOAD_DATA_BY_ID',
    data: { requestId }
  });

  // 返回清理函数
  return () => {
    clearTimeout(timeoutId);
    cleanup();
  };
}
