// 下载项
export interface DownloadItem {
  requestId: string;
  url: string;
  filename?: string;
  title?: string; // 视频标题
  size?: string;
  domain?: string; // 域名
  ext?: string; // 文件扩展名
  favIconUrl?: string; // 网站图标URL
  pageTitle?: string; // 页面标题
  pageUrl?: string; // 页面URL
  requestHeaders?: Array<{ name: string; value: string }>;
  isM3u8?: boolean;
  duration?: string; // 视频时长
  status?: 'completed' | 'downloading' | 'failed'; // 下载状态
  isLiveStream?: boolean; // 是否为直播流
}

// 下载状态
export const DownloadStatus = {
  DOWNLOADING: 'downloading',
  LIVE_RECORDING: 'live_recording', // 直播录制中
  COMPLETED: 'completed',
  SAVED: 'saved',
  ERROR: 'error'
} as const;

export type DownloadStatus = typeof DownloadStatus[keyof typeof DownloadStatus];

// 下载进度
export interface DownloadProgress {
  [requestId: string]: {
    percentage: number;
    downloadedSize: number;
    totalSize?: number;
    speed?: number;
    status: DownloadStatus;
    errorText?: string; // 只在错误状态时设置
  };
}

// 下载响应
export interface DownloadResponse {
  success: boolean;
  error?: string;
  filename?: string;
  data?: {
    filename?: string;
    [key: string]: unknown;
  };
}

// 消息类型
export interface PageMessage {
  type: string;
  data: Record<string, unknown>;
}

// M3U8变体流
export interface M3u8Variant {
  bandwidth: number;
  resolution: string;
  url: string;
}

// M3U8质量选项
export interface M3u8QualityOption {
  quality: number;
  qualityLabel: string;
  content: string;
}

// 下载配置
export interface DownloadConfig {
  requestId: string;
  url: string;
  filename: string;
  pageTaskId?: string;
}

// 下载控制器状态
export const DownloadControllerStatus = {
  IDLE: 'idle',
  DOWNLOADING: 'downloading',
  COMPLETED: 'completed',
  ERROR: 'error'
} as const;

export type DownloadControllerStatus = typeof DownloadControllerStatus[keyof typeof DownloadControllerStatus];

// 下载结果
export interface DownloadResult {
  success: boolean;
  filename: string;
  blobUrl?: string;
  error?: string;
}

// 下载控制器
export interface IDownloadController {
  status: DownloadControllerStatus;
  progress: {
    percentage: number;
    downloadedSize: number;
    totalSize?: number;
    speed?: number;
  };
  start(): Promise<DownloadResult>;
  cancel(): void; // 取消下载
  onProgress?: (progress: { percentage: number; downloadedSize: number; totalSize?: number; speed?: number }) => void;
  onStatusChange?: (status: DownloadControllerStatus) => void;
}

// 插件检测状态
export enum ExtensionStatus {
  DETECTING = 'DETECTING',    // 检测中
  INSTALLED = 'INSTALLED',    // 已安装
  NOT_INSTALLED = 'NOT_INSTALLED'  // 未安装
}

// 应用状态
export interface AppState {
  downloadData: DownloadItem | null;
  downloadProgress: DownloadProgress;
  savedFiles: Set<string>;
  pageTaskId: string | null;
  contentScriptReady: boolean;
  pageReady: boolean;
  isLoading: boolean;
  error: string | null;
  downloadController: IDownloadController | null;
  downloadBlobUrl: string | null;
  liveRecordingController: LiveRecordingController | null; // 直播录制控制器
  extensionStatus: ExtensionStatus; // 插件检测状态
}

// 标准下载Hook属性
export interface UseStandardDownloadProps {
  state: AppState;
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void;
  setDownloadController: (controller: IDownloadController) => void;
  setDownloadBlobUrl: (blobUrl: string | null) => void;
  updateDownloadDataDuration: (duration: string | null) => void;
  saveToLocal: (blobUrl?: string) => Promise<void>;
}

// M3U8下载Hook属性
export interface UseM3u8DownloadProps {
  state: AppState;
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void;
  setDownloadBlobUrl: (blobUrl: string | null) => void;
  updateDownloadDataDuration: (duration: string | null) => void;
  saveToLocal: (blobUrl?: string) => Promise<void>;
  setLiveRecordingController: (controller: LiveRecordingController | null) => void; // 设置直播录制控制器
  stopRecording?: (requestId: string) => void; // 停止录制回调
}

// 下载控制Hook属性
export interface UseDownloadControlProps {
  state: AppState; // 应用状态对象，包含下载数据、进度、控制器等信息
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void; // 更新下载进度的回调函数
  markFileAsSaved: (requestId: string) => void; // 标记文件为已保存状态的回调函数
}

// 直播流检测结果
export interface LiveStreamDetectionResult {
  isLiveStream: boolean;
  hasEndList: boolean;
  totalSize?: number;
  reason?: string; // 判断为直播流的原因
}

// 直播录制控制器
export interface LiveRecordingController {
  requestId: string;
  abortController: AbortController;
  isRecording: boolean;
  recordedSegments: ArrayBuffer[];
  startTime: number;
  stop: () => Promise<void>;
}
